{"cells": [{"cell_type": "markdown", "id": "34b1cebf", "metadata": {}, "source": ["# Filtragem e Limpeza de Dados\n", "\n", "## <PERSON><PERSON><PERSON>\n", "Este documento apresenta uma análise detalhada do processo de filtragem e limpeza aplicado ao dataset de vendas, que inicialmente continha **40.291 registros** e resultou em **18.367 registros** após as filtragens, representando uma **retenção de 45,59%** dos dados originais.\n", "\n", "## Resumo Executivo\n", "- **Total de registros removidos**: 21.924 (54,41%)\n", "- **Total de registros mantidos**: 18.367 (45,59%)\n", "- **Objetivo**: Garantir a qualidade e consistência dos dados para análises posteriores\n", "- **Implementação**: <PERSON><PERSON><PERSON><PERSON>data_filtering.py` para uso em múltiplos notebooks\n", "\n", "## Filtros Aplicados e Seus Impactos\n", "\n", "### 1. Remoção de Devoluções de Mercadoria\n", "- **Registros removidos**: 1.281 (3,18%)\n", "- **Regis<PERSON>s restantes**: 39.010\n", "- **Justificativa**: Devoluções podem distorcer análises de vendas e comportamento de compra, sendo tratadas separadamente.\n", "\n", "### 2. Validação de Preços Mínimos\n", "- **Filtro**: Preço Varejo > R$ 1,00\n", "- **Registros removidos**: 467 (1,16%)\n", "- **<PERSON><PERSON>s restantes**: 38.543\n", "- **Justificativa**: Preços muito baixos podem indicar erros de sistema ou produtos de teste.\n", "\n", "### 3. Validação de Total de Preços\n", "- **Filtro**: Total Preço Varejo > R$ 1,00\n", "- **Registros removidos**: 0 (0,00%)\n", "- **<PERSON><PERSON>s restantes**: 38.543\n", "- **Observação**: Nenhum registro foi removido, indicando consistência após o filtro anterior.\n", "\n", "### 4. Validação de Cálculo de Valor Total\n", "- **Filtro**: Diferença entre cálculo manual e valor registrado < R$ 1,00\n", "- **Registros removidos**: 0 (0,00%)\n", "- **<PERSON><PERSON>s restantes**: 38.543\n", "- **Fórmula validada**: Quantidade × Preço Varejo - Desconto = Valor Total\n", "\n", "### 5. **Filtro de Maior Impacto**: Validação de Idade dos Clientes\n", "- **Filtro**: Idade entre 10 e 100 anos na data da venda\n", "- **Registros removidos**: 19.255 (47,79%)\n", "- **<PERSON><PERSON><PERSON> restantes**: 19.288\n", "- **Impacto**: <PERSON>ste foi o filtro mais restritivo, removendo quase metade dos dados\n", "- **Possíveis causas**: \n", "  - Datas de nascimento inválidas ou não preenchidas\n", "  - Erros na coleta de dados demográficos\n", "  - Campos de data corrompidos\n", "\n", "### 6. Validação de Idade no Cadastro\n", "- **Filtro**: Idade ≥ 18 anos na data do cadastro\n", "- **Registros removidos**: 594 (1,47%)\n", "- **<PERSON><PERSON>s restantes**: 18.694\n", "- **Justificativa**: Conformidade legal para cadastros de clientes.\n", "\n", "### 7. Remoção de Duplicatas\n", "- **<PERSON><PERSON><PERSON><PERSON>**: Mesmo DOC_UNICO, ID_Produto e ID_Cliente\n", "- **Registros removidos**: 96 (0,24%)\n", "- **<PERSON><PERSON>s restantes**: 18.598\n", "- **Observação**: Baixo número de duplicatas indica boa qualidade inicial dos dados.\n", "\n", "### 8. Validação de Valor Total com Tolerância\n", "- **Filtro**: Diferença ≤ R$ 2,00 entre cálculo e valor registrado\n", "- **Registros removidos**: 0 (0,00%)\n", "- **<PERSON><PERSON>s restantes**: 18.598\n", "- **Resultado**: Confirmação da consistência dos cálculos.\n", "\n", "### 9. Validação de Lojas\n", "- **Filtro**: Lojas com Tipo PDV válido (não nulo)\n", "- **Registros removidos**: 231 (0,57%)\n", "- **<PERSON><PERSON>s restantes**: 18.367\n", "- **Impacto**: Remoção de vendas sem identificação adequada do ponto de venda.\n", "\n", "### 10. Validação de Produtos\n", "- **Filtros aplicados**:\n", "  - Produtos com nome válido: 0 registros removidos\n", "  - Produtos com grupo válido: 0 registros removidos\n", "- **Resultado**: Todos os produtos restantes possuem informações básicas completas.\n", "\n", "## <PERSON><PERSON><PERSON><PERSON> dos Resultados\n", "\n", "### Principais Insights\n", "1. **Qualidade inicial satisfatória**: Apenas 4,34% dos dados foram removidos por inconsistências de preços e cálculos\n", "2. **Problema crítico com dados demográficos**: 47,79% dos registros tinham problemas de idade, indicando necessidade de melhoria na coleta destes dados\n", "3. **Baixa duplicação**: Apenas 0,24% de registros duplicados demonstra bom controle na origem dos dados\n", "4. **Consistência de produtos e lojas**: Poucos problemas encontrados nestes domínios\n", "\n", "### Impacto da Limpeza para Análises Futuras\n", "Com 45,59% dos dados originais mantidos, o dataset filtrado oferece:\n", "- **<PERSON><PERSON> confiabilida<PERSON>** nas análises de comportamento de compra\n", "- **Consistência** nos cálculos financeiros\n", "- **Conformidade** com regras de negócio e requisitos legais\n", "- **Base sólida** para modelagem preditiva e análises estatísticas"]}, {"cell_type": "code", "execution_count": null, "id": "30921262", "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "\n", "# Adicionar o diretório pai ao path para importar o módulo de filtragem\n", "sys.path.append(os.path.dirname(os.path.abspath('.')))\n", "\n", "# Importar as funções do módulo de filtragem centralizado\n", "from data_filtering import apply_business_filters, get_filtering_summary\n", "\n", "df_filtrado = apply_business_filters('../assets/dados.csv')\n", "\n", "# Obter resumo detalhado em formato de tabela\n", "resumo_filtros = get_filtering_summary('../assets/dados.csv')\n", "print(resumo_filtros.to_string(index=False))\n", "\n", "# Calcular estatísticas finais\n", "linhas_iniciais = resumo_filtros.iloc[0]['<PERSON><PERSON> Restantes']\n", "linhas_finais = resumo_filtros.iloc[-1]['<PERSON><PERSON> Restantes']\n", "total_removidas = linhas_iniciais - linhas_finais\n", "percentual_mantido = (linhas_finais / linhas_iniciais * 100)\n", "percentual_removido = (total_removidas / linhas_iniciais * 100)\n", "\n", "print(\"\\nEstatísticas Finais:\")\n", "print(f\"Linhas iniciais: {linhas_iniciais:,}\")\n", "print(f\"Linhas finais: {linhas_finais:,}\")\n", "print(f\"Total de linhas removidas: {total_removidas:,}\")\n", "print(f\"Percentual de dados mantidos: {percentual_mantido:.2f}%\")\n", "print(f\"Percentual de dados removidos: {percentual_removido:.2f}%\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}