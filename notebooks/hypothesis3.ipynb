{"cells": [{"cell_type": "markdown", "id": "91e59365", "metadata": {}, "source": ["## Hipótese para problemática 3 — Público Ideal\n", "\n", "**Hipótese de Pesquisa**  \n", "- H₀: A participação de lentes de grau nas vendas **é a mesma** entre estados/cidades.  \n", "- H₁: A participação de lentes de grau **varia** por estado/cidade.  \n", "\n", "**Justificativa**  \n", "Analisar se o impacto das Óticas Chilli Beans é o mesmo entre as diferentes localidades. Dessa forma, verificar se a participação das vendas de lentes de grau difere entre localidades (UF/cidade) para orientar decisões de sortimento, estoque e campanhas regionais.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "6db35947", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Chi2 = 233.2653358067207\n", "p-value = 3.1196654946503738e-27\n", "dof = 45\n", "\n", "Observado (primeiras 10 linhas):\n", "Tipo_Item               GRAU  SOLAR\n", "Dim_Cliente.Uf_Cliente             \n", "AC                         4     61\n", "AL                         8    215\n", "AL                         0      2\n", "AM                        58    255\n", "AM                         0      4\n", "AP                         8     40\n", "AP                         1      2\n", "BA                        35    835\n", "CE                        10    177\n", "DF                        45    594\n", "\n", "Esperado (primeiras 10 linhas):\n", "Tipo_Item                    GRAU       SOLAR\n", "Dim_Cliente.Uf_Cliente                       \n", "AC                       5.795302   59.204698\n", "AL                      19.882345  203.117655\n", "AL                       0.178317    1.821683\n", "AM                      27.906610  285.093390\n", "AM                       0.356634    3.643366\n", "AP                       4.279608   43.720392\n", "AP                       0.267475    2.732525\n", "BA                      77.567894  792.432106\n", "CE                      16.672639  170.327361\n", "DF                      56.972281  582.027719\n", "\n", "Resultado: p < 0.05 → há evidência de que a participação de lentes de grau varia entre UFs.\n"]}], "source": ["import pandas as pd\n", "from scipy.stats import chi2_contingency\n", "\n", "\n", "def test_lentes_por_geo(csv_path='../assets/dados.csv', geo_col='Dim_Cliente.Uf_Cliente'):\n", "    \"\"\"<PERSON>ega dados, mapeia Tipo_Item e realiza teste qui-quadrado por coluna geográfica.\n", "\n", "    Parâmetros:\n", "    - csv_path: caminho para o CSV\n", "    - geo_col: coluna geo<PERSON>r<PERSON> a usar (ex.: 'Dim_Cliente.Uf_Cliente' ou 'Dim_Cliente.Cidade_cliente')\n", "\n", "    Retorna:\n", "    - dict com tabela observada, estatísticas e frequências esperadas\n", "    \"\"\"\n", "    df = pd.read_csv(csv_path)\n", "\n", "    # Normalizar e mapear grupo de produto para GRAU/SOLAR\n", "    df['produto_grupo'] = df['Dim_Produtos.Grupo_Produto'].astype(str).str.strip().str.upper()\n", "    df['produto_grupo'] = df['produto_grupo'].str.replace('Ó', 'O')\n", "    df['Tipo_Item'] = pd.NA\n", "    df.loc[df['produto_grupo'].str.contains('LENTES', na=False), 'Tipo_Item'] = 'GRAU'\n", "    df.loc[df['produto_grupo'].str.contains('SOLAR|OCULOS|OCULOS SOLARES|OCULOS DE SOL', na=False) & ~df['produto_grupo'].str.contains('LENTES', na=False), 'Tipo_Item'] = 'SOLAR'\n", "\n", "    # Remover linhas sem classificacao de tipo\n", "    df = df.dropna(subset=['Tipo_Item', geo_col])\n", "\n", "    # Montar tabela de contingência\n", "    tab = pd.crosstab(df[geo_col], df['Tipo_Item'])\n", "\n", "    # Se houver muitas categorias com baixo total, podemos agrupar as menos frequentes (opcional)\n", "    # Executar teste qui-quadrado\n", "    chi2, p, dof, expected = chi2_contingency(tab)\n", "\n", "    return {\n", "        'observed': tab,\n", "        'chi2': chi2,\n", "        'p_value': p,\n", "        'dof': dof,\n", "        'expected': pd.DataFrame(expected, index=tab.index, columns=tab.columns)\n", "    }\n", "\n", "\n", "# Exemplo de uso: teste por UF\n", "res_uf = test_lentes_por_geo('../assets/dados.csv', geo_col='Dim_Cliente.Uf_Cliente')\n", "#print('Ta<PERSON>a observada (UF x Tipo_Item) - top 10 UFs por total:')\n", "#print(res_uf['observed'].sum(axis=1).sort_values(ascending=False).head(10))\n", "print('\\nChi2 =', res_uf['chi2'])\n", "print('p-value =', res_uf['p_value'])\n", "print('dof =', res_uf['dof'])\n", "\n", "# Mostrar parte da tabela e esperados para inspeção\n", "print('\\nObservado (primeiras 10 linhas):')\n", "print(res_uf['observed'].head(10))\n", "print('\\nEsperado (primeiras 10 linhas):')\n", "print(res_uf['expected'].head(10))\n", "\n", "# Se p < 0.05, reje<PERSON> H0: proporções diferem entre as UFs\n", "if res_uf['p_value'] < 0.05:\n", "    print('\\nResultado: p < 0.05 → há evidência de que a participação de lentes de grau varia entre UFs.')\n", "else:\n", "    print('\\nResultado: p >= 0.05 → não há evidência suficiente para rejeitar H0.')\n"]}, {"cell_type": "markdown", "id": "31375584", "metadata": {}, "source": ["**Interpretação:**\n", "\n", "O teste qui-quadrado indicou diferença significativa na distribuição das vendas entre as localidades (p < 0.05).\n", "Isso nos leva a rejeitar H₀ e aceitar H₁: a participação de lentes de grau nas vendas varia de acordo com o estado/cidade.\n", "Observamos que algumas localidades apresentam maior proporção de vendas de lentes de grau, enquanto outras concentram-se mais em óculos solares, sugerindo que o comportamento de consumo não é uniforme e que há necessidade de estratégias regionais de sortimento e marketing."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}