{"cells": [{"cell_type": "markdown", "id": "b7b90ce4", "metadata": {}, "source": ["# Análise de Vendas por Tipo de PDV e Subgrupo de Produtos\n", "\n", "Este notebook realiza uma exploração das vendas de óticas, estruturando e visualizando métricas por tipo de ponto de venda (PDV) e por subgrupo de produtos.\n", "\n", "## 1. Carregamento e Preparação dos Dados\n", "- **Importação do módulo de filtragem**: Utiliza o módulo `data_filtering.py` que centraliza todas as regras de negócio\n", "- **Aplicação automática dos filtros**: Todos os filtros do `filtering.ipynb` são aplicados automaticamente:\n", "  - Remoção de devoluções de mercadoria\n", "  - Validação de preços mínimos (> R$ 1,00)\n", "  - Validação de cálculos de valor total\n", "  - Filtros de idade dos clientes (10-100 anos na venda, ≥18 anos no cadastro)\n", "  - Remoção de duplicatas\n", "  - Validação de lojas e produtos\n", "  - Limpeza de espaços em branco em colunas categóricas\n", "- **Resultado**: Dataset limpo e consistente para análises posteriores\n", "- Filtragem dos tipos de PDV de interesse: `[\"LOJA DE RUA OTICO\", \"LOJA OTICO\", \"QUIOSQUE OTICO\"]`, gerando `df_oticas`.\n", "\n", "## 2. Construção de Matrizes (Pivot Tables)\n", "Foram criadas várias visões agregadas:\n", "\n", "| Objeto | Descrição |\n", "|--------|-----------|\n", "| `pivot` / `pivot_mean` | <PERSON><PERSON><PERSON> de `Valor_Total` por Tipo de PDV vs Subgrupo de Produto. (São equivalentes no contexto atual.) |\n", "| `pivot_qty` | <PERSON><PERSON> das quantidades vendidas (`Quantidade`). |\n", "| `pivot_count` | Contagem de registros (número de transações) por combinação. |\n", "| `df_pivot` | Série temporal (amostra exibida) agregada por data (`ID_Date`) e tipo de PDV. |\n", "\n", "## 3. Visual<PERSON><PERSON><PERSON><PERSON>\n", "- Heatmap 1: Média de valor vendido por tipo de loja vs subgrupo (escala “Purples”).\n", "- Heatmap 2: Volume físico (quantidade) por tipo de loja vs subgrupo (escala “Greens”).\n", "- Rotação de rótulos, t<PERSON><PERSON><PERSON>, legendas e anotações para leitura direta.\n", "\n", "## 4. Métricas e Séries Resumidas\n", "| Série / Variável | Significado |\n", "|------------------|-------------|\n", "| `product_mean` | <PERSON><PERSON>dia de `Valor_Total` por subgrupo (todas as lojas). |\n", "| `product_totals` | Média priorizando subgrupos com maior ticket médio (exibe ordenação decrescente). |\n", "| `qty_totals` | Volume total vendido (quantidades somadas) por subgrupo. |\n", "| `transaction_totals` | Contagem de transações (registros) por subgrupo (proxy de frequência). |\n", "| `media_manual` | Valor médio calculado manualmente para um subconjunto (validação). |\n", "| `soma_valores` | Soma total dos valores considerados (consistência). |\n", "| `total_vendas` | Número total de registros processados no recorte analisado. |\n", "| `tipo_loja` / `idx` | Seleção focal de um tipo de PDV para detalhamento. |\n", "\n", "## 5. <PERSON><PERSON>ques Iniciais (Insight Exploratórios)\n", "- Subgrupos de maior ticket médio: PROGRESSIVA, MULTIFOCAIS, BLOCO VISAO SIMPLES e SEMI PROGRESSIVA dominam a receita média.\n", "- Itens de alto volume mas menor valor médio: VISAO SIMPLES, LICENCIADO, CLASSICO, MULTI evidenciam giro.\n", "- Diferenças entre tipos de PDV:\n", "    - `LOJA OTICO` concentra variedade e amplitude (maiores contagens).\n", "    - `LOJA DE RUA OTICO` mostra picos específicos (e.g. PROGRESSIVA elevada).\n", "    - `QUIOSQUE OTICO` apresenta portfólio mais enxuto e valores medianos moderados."]}, {"cell_type": "code", "execution_count": null, "id": "a812b1be", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Carregando e aplicando filtros de negócio...\n"]}], "source": ["import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import sys\n", "import os\n", "\n", "# Adicionar o diretório pai ao path para importar o módulo de filtragem\n", "sys.path.append(os.path.dirname(os.path.abspath('.')))\n", "\n", "# Importar o módulo de filtragem\n", "from data_filtering import apply_business_filters\n", "\n", "# Carregar e filtrar os dados usando o módulo reutilizável\n", "df = apply_business_filters('../assets/dados.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "be780313", "metadata": {}, "outputs": [], "source": ["# Filtrar apenas os tipos de PDV desejados\n", "tipos_pdv = [\"LOJA DE RUA OTICO\", \"LOJA OTICO\", \"QUIOSQUE OTICO\"]\n", "df_oticas = df[df['Dim_Lojas.Tipo_PDV'].isin(tipos_pdv)]\n", "\n", "# Criando uma tabela pivot: lojas x tipos de produtos com média das vendas\n", "pivot = df_oticas.pivot_table(\n", "    index='Dim_Lojas.Tipo_PDV',\n", "    columns='Dim_Produtos.Sub_Grupo',\n", "    values='Valor_Total',\n", "    aggfunc='mean',\n", "    fill_value=0\n", ")\n", "\n", "if pivot.size > 0:\n", "    # Plotando o heatmap\n", "    plt.figure(figsize=(14,8))\n", "    \n", "    # Annot para mostrar os valores\n", "    # fmt=\".0f\" para mostrar apenas valores inteiros\n", "    # cmap=\"YlGnBu\" para usar uma paleta de roxo\n", "    # cbar_kws={'label': 'Valor Total (R$)'} para adicionar um label à barra de cores\n", "    sns.heatmap(pivot, annot=True, fmt=\".0f\", cmap=\"Purples\", cbar_kws={'label': 'Valor Total (R$)'})\n", "    \n", "    plt.title(\"Valor Total de Vendas por Tipo de Loja e Produto\")\n", "    plt.xlabel(\"Produto\")\n", "    plt.ylabel(\"Loja\")\n", "    plt.xticks(rotation=45, ha='right')\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(f\"\\nTOP 5 produtos por valor médio (geral, sem separar por tipo de loja):\")\n", "    # Calculando a média diretamente dos dados originais, sem agregar por tipo de PDV\n", "    product_mean = df_oticas.groupby('Dim_Produtos.Sub_Grupo')['Valor_Total'].mean().sort_values(ascending=False)\n", "    print(product_mean.head())\n", "else:\n", "    print(\"ERRO: Tabela auxiliar vazia!\")"]}, {"cell_type": "code", "execution_count": null, "id": "3e0a7462", "metadata": {}, "outputs": [], "source": ["# Opção 4: Quantidade total vendida (volume físico)\n", "pivot_qty = df_oticas.pivot_table(\n", "    index='Dim_Lojas.Tipo_PDV',\n", "    columns='Dim_Produtos.Sub_Grupo',\n", "    values='Quantidade',  # Usando quantidade em vez de valor\n", "    aggfunc='sum',\n", "    fill_value=0\n", ")\n", "\n", "plt.figure(figsize=(14,8))\n", "sns.heatmap(pivot_qty, annot=True, fmt=\".0f\", cmap=\"Greens\", cbar_kws={'label': 'Quantidade Total'})\n", "plt.title(\"Volume de Produtos Vendidos por Tipo de Loja e Produto\")\n", "plt.xlabel(\"Produto\")\n", "plt.ylabel(\"Loja\")\n", "plt.xticks(rotation=45, ha='right')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"\\nTOP 5 produtos por quantidade total vendida (geral, sem separar por tipo de loja):\")\n", "qty_totals = pivot_qty.sum(axis=0).sort_values(ascending=False)\n", "print(qty_totals.head())"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}