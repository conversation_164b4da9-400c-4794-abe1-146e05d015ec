{"cells": [{"cell_type": "markdown", "id": "8a2cc9b5", "metadata": {}, "source": ["# An<PERSON><PERSON><PERSON> de Ticket Médio por Tipo de PDV e Região\n", "\n", "Este notebook realiza uma análise exploratória das vendas em óticas, avaliando a métrica de **ticket médio** por tipo de ponto de venda (PDV) e por região do Brasil.\n", "\n", "## 1. Carregamento e Preparação dos Dados\n", "\n", "* Leitura do arquivo CSV.\n", "* Limpeza de espaços em branco em colunas categóricas: `Dim_Lojas.Tipo_PDV` e `Dim_Cliente.Uf_Cliente`.\n", "* Criação de uma nova coluna `<PERSON><PERSON><PERSON>`, mapeando cada U<PERSON> (estado) para uma das 5 regiões oficiais do Brasil (Norte, Nordeste, Centro-Oeste, Sudeste e Sul).\n", "* Filtragem para manter apenas os tipos de PDV de interesse:\n", "  `[\"LOJA DE RUA OTICO\", \"LOJA OTICO\", \"QUIOSQUE OTICO\"]`.\n", "\n", "## 2. Construção da Métrica de Análise\n", "\n", "* Definição de **Ticket <PERSON>** como:\n", "\n", "$$\n", "Ticket\\_Medio = \\frac{Valor\\_Total}{Quantidade}\n", "$$\n", "\n", "* Agregação dos dados por `Dim_Lojas.Tipo_PDV` (tipo de loja) e `Regiao` (localização geográfica).\n", "* Cálculo da média do ticket médio em cada combinação, resultando em uma matriz (`ticket_medio`) com:\n", "\n", "  * Linhas = Tipos de PDV.\n", "  * Colunas = Regiões do Brasil.\n", "  * Valores = Média do ticket médio.\n", "\n", "## 3. Visual<PERSON><PERSON><PERSON><PERSON>\n", "\n", "Foram gerados dois gráficos complementares a partir da matriz `ticket_medio`:\n", "\n", "* **Gráfico de Barras:**\n", "\n", "  * Eixo X: Tipos de PDV.\n", "  * Eixo Y: Ticket <PERSON> (R\\$).\n", "  * Barras coloridas representam as regiões.\n", "  * Permite comparar, em valores absolutos, como cada tipo de loja performa em diferentes regiões.\n", "\n", "* **Heatmap (Mapa de Calor):**\n", "\n", "  * Matriz colorida com os mesmos dados agregados.\n", "  * Uso de escala contínua (“YlGnBu”) para destacar contrastes.\n", "  * Anotações exibem os valores numéricos de ticket médio em cada célula.\n", "  * Facilita a leitura de padrões e comparações rápidas entre regiões e tipos de PDV.\n", "\n", "## 4. <PERSON><PERSON>ques Iniciais (Insights Exploratórios)\n", "\n", "* O ticket médio varia significativamente entre regiões, sugerindo diferenças de poder de compra ou perfil de consumo.\n", "* `LOJA OTICO` tende a apresentar maior diversidade de valores entre regiões, refletindo sua presença mais abrangente.\n", "* `LOJA DE RUA OTICO` mostra disparidades mais claras, com picos regionais.\n", "* `QUIOSQUE OTICO` mantém comportamento mais estável, com valores medianos moderados.\n", "* O uso conjunto do gráfico de barras e do heatmap permite tanto comparar magnitudes absolutas (barras) quanto identificar padrões visuais e contrastes (heatmap).\n"]}, {"cell_type": "code", "execution_count": 1, "id": "d87a1e65", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['NORTE' 'CENTRO-OESTE' 'SUDESTE' 'SUL' 'NORDESTE']\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import sys\n", "import os\n", "\n", "# Adicionar o diretório pai ao path para importar o módulo de filtragem\n", "sys.path.append(os.path.dirname(os.path.abspath('.')))\n", "\n", "# Importar o módulo de filtragem\n", "from data_filtering import apply_business_filters\n", "\n", "# Carregar e filtrar os dados usando o módulo reutilizável\n", "df = apply_business_filters('../assets/dados.csv')"]}, {"cell_type": "code", "execution_count": 3, "id": "e9258055", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1400x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Canais de venda únicos após processamento: ['ECO CHILLI', 'OTICA', 'VERMELHA']\n", "Regiões disponíveis: ['CENTRO-OESTE', 'NORDESTE', 'NORTE', 'SUDESTE', 'SUL']\n", "\n", "<PERSON><PERSON><PERSON> da matriz ticket_medio: (3, 5)\n"]}], "source": ["# Criando a nova coluna de Regiões\n", "df['Regiao'] = df['Dim_Lojas.REGIAO_CHILLI']\n", "\n", "# Filtrar apenas os tipos de PDV desejados\n", "df_oticas = df\n", "\n", "# Criar a coluna de Ticket Médio (Valor_Total dividido pela Quantidade)\n", "df_oticas[\"Ticket_Medio\"] = df_oticas[\"Valor_Total\"] / df_oticas[\"Quantidade\"]\n", "\n", "# Agrupar por Canal de Venda (CANAL_VENDA) e Região, calcular média do ticket médio\n", "ticket_medio = (\n", "    df_oticas.groupby([\"Dim_Lojas.CANAL_VENDA\", \"Regiao\"])[\"Ticket_Medio\"]\n", "    .mean()\n", "    .unstack()\n", "    .fillna(0)  # <PERSON><PERSON><PERSON> valores NaN com 0\n", ")\n", "\n", "# Configurações de estilo para os gráficos\n", "plt.style.use('seaborn-v0_8-darkgrid')\n", "plt.rcParams['figure.facecolor'] = 'white'\n", "plt.rcParams['axes.facecolor'] = 'white'\n", "\n", "# Plotar gráfico de barras com estilo melhorado\n", "fig, ax = plt.subplots(figsize=(14, 8))\n", "\n", "# Cores personalizadas para as regiões\n", "cores_regioes = {\n", "    'CENTRO-OESTE': '#FF6B6B',\n", "    'NORDESTE': '#4ECDC4', \n", "    'NORTE': '#45B7D1',\n", "    'SUDESTE': '#96CEB4',\n", "    'SUL': '#FFEAA7'\n", "}\n", "\n", "# Plotar com cores personalizadas\n", "ticket_medio.plot(kind=\"bar\", ax=ax, color=[cores_regioes.get(col, '#95A5A6') for col in ticket_medio.columns])\n", "\n", "# Melhorias no estilo\n", "ax.set_title(\"Ticket Médio por Canal de Venda e Região\", fontsize=18, fontweight='bold', pad=20)\n", "ax.set_ylabel(\"Ticket Médio (R$)\", fontsize=14, fontweight='bold')\n", "ax.set_xlabel(\"Canal de Venda\", fontsize=14, fontweight='bold')\n", "\n", "# Configurar a legenda\n", "ax.legend(title=\"<PERSON><PERSON><PERSON>\", bbox_to_anchor=(1.05, 1), loc=\"upper left\", \n", "          title_fontsize=12, fontsize=11, frameon=True, fancybox=True, shadow=True)\n", "\n", "# Rotacionar labels do eixo x para melhor legibilidade\n", "plt.setp(ax.get_xticklabels(), rotation=45, ha='right', fontsize=11)\n", "\n", "# Adicionar grid mais sutil\n", "ax.grid(True, alpha=0.3, linestyle='--')\n", "\n", "# Formatar valores no eixo y como moeda brasileira\n", "ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'R$ {x:,.0f}'))\n", "\n", "# Adicionar valores nas barras\n", "for container in ax.containers:\n", "    ax.bar_label(container, fmt='R$ %.0f', rotation=90, fontsize=9, padding=3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Exibir informações sobre os dados processados\n", "print(f\"Canais de venda únicos após processamento: {sorted(df_oticas['Dim_Lojas.CANAL_VENDA'].unique())}\")\n", "print(f\"Regiões disponíveis: {sorted(df_oticas['Regiao'].unique())}\")\n", "print(f\"\\nShape da matriz ticket_medio: {ticket_medio.shape}\")"]}, {"cell_type": "code", "execution_count": 20, "id": "c278336f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🏆 TOP 5 TICKET MÉDIO POR CANAL DE VENDA E REGIÃO\n", "============================================================\n", "1º lugar: OTICA - SÃO PAULO: R$ 345\n", "2º lugar: OTICA - NORDESTE: R$ 320\n", "3º lugar: OTICA - CENTRO-OESTE: R$ 319\n", "4º lugar: OTICA - SUDESTE: R$ 315\n", "5º lugar: OTICA - SUL: R$ 312\n", "\n", "============================================================\n", "\n", "📊 TICKET MÉDIO POR CANAL DE VENDA (MÉDIA GERAL)\n", "==================================================\n", "OTICA: R$ 321\n", "VERMELHA: R$ 269\n", "\n", "🌎 TICKET MÉDIO POR REGIÃO (MÉDIA GERAL)\n", "========================================\n", "SÃO PAULO: R$ 283\n", "NORDESTE: R$ 282\n", "SUL: R$ 281\n", "CENTRO-OESTE: R$ 276\n", "SUDESTE: R$ 276\n", "NORTE: R$ 260\n"]}], "source": ["# --- <PERSON><PERSON><PERSON> o Top 5 geral com melhor formatação ---\n", "top5 = (\n", "    ticket_medio.reset_index()  # tira MultiIndex (Canal e Região viram colunas)\n", "    .melt(id_vars=\"Dim_Lojas.CANAL_VENDA\", var_name=\"Regiao\", value_name=\"Ticket_Medio\")  # formato longo\n", "    .dropna(subset=[\"Ticket_Medio\"])  # remove NaN\n", "    .query(\"Ticket_Medio > 0\")  # remove valores zero\n", "    .sort_values(\"Ticket_Medio\", ascending=False)  # ordena do maior pro menor\n", "    .head(5)  # pega os 5 primeiros\n", "    .reset_index(drop=True)\n", ")\n", "\n", "print(\"🏆 TOP 5 TICKET MÉDIO POR CANAL DE VENDA E REGIÃO\")\n", "print(\"=\" * 60)\n", "for i, row in top5.iterrows():\n", "    posicao = i + 1\n", "    canal = row['Dim_Lojas.CANAL_VENDA']\n", "    regiao = row['Regiao']\n", "    valor = row['Ticket_Medio']\n", "    print(f\"{posicao}º lugar: {canal} - {regiao}: R$ {valor:,.0f}\")\n", "\n", "print(\"\\n\" + \"=\" * 60)\n", "\n", "# Análise adicional por canal\n", "print(\"\\n📊 TICKET MÉDIO POR CANAL DE VENDA (MÉDIA GERAL)\")\n", "print(\"=\" * 50)\n", "ticket_por_canal = df_oticas.groupby('Dim_Lojas.CANAL_VENDA')['Ticket_Medio'].mean().sort_values(ascending=False)\n", "for canal, valor in ticket_por_canal.items():\n", "    print(f\"{canal}: R$ {valor:,.0f}\")\n", "\n", "# Análise adicional por região\n", "print(\"\\n🌎 TICKET MÉDIO POR REGIÃO (MÉDIA GERAL)\")\n", "print(\"=\" * 40)\n", "ticket_por_regiao = df_oticas.groupby('Regiao')['Ticket_Medio'].mean().sort_values(ascending=False)\n", "for regiao, valor in ticket_por_regiao.items():\n", "    print(f\"{regiao}: R$ {valor:,.0f}\")"]}, {"cell_type": "code", "execution_count": null, "id": "39c3b052", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from matplotlib.colors import LinearSegmentedColormap\n", "\n", "# Criar colormap personalizado mais adequado para ticket médio (azul → verde → amarelo → vermelho)\n", "cores_personalizadas = ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D']\n", "cmap = LinearSegmentedColormap.from_list(\"ticket_medio\", cores_personalizadas, N=256)\n", "\n", "# Configurar o estilo\n", "plt.style.use('seaborn-v0_8-white')\n", "\n", "# Plotar gráfico de heatmap melhorado\n", "fig, ax = plt.subplots(figsize=(12, 8))\n", "\n", "# Criar o heatmap com melhorias visuais\n", "sns.heatmap(ticket_medio, \n", "            annot=True, \n", "            fmt='.0f',  # Formato sem casas decimais para valores em reais\n", "            cmap=cmap, \n", "            cbar_kws={\n", "                'label': 'Ticket <PERSON> (R$)',\n", "                'shrink': 0.8,\n", "                'aspect': 20\n", "            },\n", "            linewidths=1,\n", "            linecolor='white',\n", "            square=False,\n", "            annot_kws={\n", "                'fontsize': 11,\n", "                'fontweight': 'bold'\n", "            },\n", "            ax=ax)\n", "\n", "# Melhorias no título e labels\n", "ax.set_title(\"Heatmap: Ticket Médio por Canal de Venda e Região\", \n", "             fontsize=18, fontweight='bold', pad=25)\n", "ax.set_xlabel(\"Região\", fontsize=14, fontweight='bold')\n", "ax.set_ylabel(\"Canal de Venda\", fontsize=14, fontweight='bold')\n", "\n", "# Melhorar a formatação dos labels\n", "plt.setp(ax.get_xticklabels(), rotation=45, ha='right', fontsize=12)\n", "plt.setp(ax.get_yticklabels(), rotation=0, fontsize=12)\n", "\n", "# Adicionar borda ao redor do heatmap\n", "for spine in ax.spines.values():\n", "    spine.set_visible(True)\n", "    spine.set_linewidth(2)\n", "    spine.set_edgecolor('black')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}