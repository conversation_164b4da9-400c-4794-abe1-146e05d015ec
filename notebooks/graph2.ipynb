{"cells": [{"cell_type": "markdown", "id": "bf1c8597", "metadata": {}, "source": ["# Análise de Compras por Faixa Etária e Grupo de Produto\n", "\n", "Este notebook calcula a idade dos clientes a partir da data de nascimento, classifica-os em faixas etárias e, em seguida, gera uma visualização que mostra quais grupos de produtos são mais consumidos em cada faixa de idade.\n", "\n", "O resultado final é um gráfico de barras empilhadas, que permite analisar a distribuição de compras de acordo com a geração do cliente."]}, {"cell_type": "markdown", "id": "16396a59", "metadata": {}, "source": ["## 1. Preparação dos Dados\n", "\n", "- Conversão da coluna `Dim_Cliente.Data_Nascimento` para o formato datetime.  \n", "- <PERSON><PERSON><PERSON><PERSON><PERSON> da idade em anos, usando a diferença entre a data atual e a data de nascimento.  \n", "- Exclusão de clientes com menos de 18 anos.  \n", "\n", "## 2. Defin<PERSON><PERSON> das Faixas Etárias\n", "\n", "As idades foram agrupadas nos seguintes intervalos:\n", "\n", "- 18–29  \n", "- 30–39  \n", "- 40–49  \n", "- 50–59  \n", "- 60–69  \n", "- 70–79  \n", "- 80+  \n", "\n", "<PERSON><PERSON> é feito com `pd.cut`, criando uma coluna categórica chamada **faixa_etaria**.\n", "\n", "## 3. Agrupamento por Faixa e Produto\n", "\n", "Os dados foram agregados com `groupby`, contabilizando a quantidade de registros por **faixa_etaria × Dim_Produtos.Grupo_Produto**.  \n", "Em seguida, a tabela foi transformada em formato matricial (`pivot`) para facilitar a construção do gráfico.\n", "\n", "## 4. Visualização\n", "\n", "- Gráfico de barras empilhadas (`stacked=True`) para mostrar a composição de cada faixa etária em termos de grupo de produto.  \n", "- Paleta de cores `tab20c` para distinguir cada categoria de produto.  \n", "- Legenda posicionada fora da área do gráfico, organizada em **3 colunas**, para evitar sobreposição.  \n", "\n", "Esse gráfico revela quais **grupos de produtos** são mais consumidos em cada faixa etária, permitindo insights sobre preferências de compra por geração.\n"]}, {"cell_type": "code", "execution_count": null, "id": "f05f5163", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import sys\n", "import os\n", "\n", "# Adicionar o diretório pai ao path para importar o módulo de filtragem\n", "sys.path.append(os.path.dirname(os.path.abspath('.')))\n", "\n", "# Importar o módulo de filtragem\n", "from data_filtering import apply_business_filters\n", "\n", "# Carregar e filtrar os dados usando o módulo reutilizável\n", "df = apply_business_filters('../assets/dados.csv')"]}, {"cell_type": "code", "execution_count": 2, "id": "a3664c64", "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "\n", "# Converter coluna de data de nascimento para datetime (se já não estiver)\n", "df['Dim_Cliente.Data_Nascimento'] = pd.to_datetime(df['Dim_Cliente.Data_Nascimento'])\n", "\n", "# Data de referência (hoje) usada para calcular idades\n", "hoje = pd.to_datetime(datetime.today().date())\n", "\n", "# Selecionar apenas colunas relevantes para este cálculo: grupo de produto e data de nascimento\n", "df_new = df[['Dim_Produtos.Grupo_Produto', 'Dim_Cliente.Data_Nascimento']].copy()\n", "\n", "# Calcular a idade inteira em anos (aproximação simples usando dias // 365)\n", "df_new['idade'] = (hoje - df_new['Dim_Cliente.Data_Nascimento']).dt.days // 365\n", "\n", "# Excluir registros de menores de 18 anos\n", "df_new = df_new[df_new['idade'] > 17]"]}, {"cell_type": "code", "execution_count": null, "id": "05010078", "metadata": {}, "outputs": [], "source": ["# Definir as faixas de idade (bins) e os rótulos correspondentes\n", "bins = [18, 29, 39, 49, 59, 69, 79, 106]  # limites inferiores e superiores\n", "labels = ['18-29', '30-39', '40-49', '50-59', '60-69', '70-79', '80+']\n", "\n", "# Criar coluna com a faixa etária usando pd.cut\n", "# right=True significa que o limite direito é incluído (ex.: 29 entra na primeira faixa)\n", "df_new['faixa_etaria'] = pd.cut(df_new['idade'], bins=bins, labels=labels, right=True)\n", "\n", "# Agrupar por faixa etária e grupo de produto, contando ocorrências\n", "agrupado = df_new.groupby(['faixa_etaria', 'Dim_Produtos.Grupo_Produto']).size().reset_index(name='qtd')\n", "agrupado.head(20)  # <PERSON><PERSON> as primeiras linhas do agrupamento para inspeção"]}, {"cell_type": "markdown", "id": "d923e90f", "metadata": {}, "source": ["## Interpretação do Gráfico\n", "\n", "- <PERSON>ada barra representa uma faixa etária.  \n", "- A altura total da barra corresponde ao número total de compras naquela faixa.  \n", "- As cores empilhadas mostram a contribuição de cada **grupo de produto**.  \n", "\n", "<PERSON><PERSON><PERSON>, é possível identificar:\n", "- Quais faixas etárias têm maior volume de compras.  \n", "- Quais produtos dominam em cada faixa (ex.: óculos entre jovens adultos, multifocais entre faixas acima de 40 anos).  \n", "- Diferenças de portfólio entre gerações.  "]}, {"cell_type": "code", "execution_count": null, "id": "433071f3", "metadata": {}, "outputs": [], "source": ["# Pivotar o dataframe 'agrupado' para ter faixas etárias nas linhas e grupos de produto nas colunas\n", "pivot = agrupado.pivot(index=\"faixa_etaria\", \n", "                       columns=\"Dim_Produtos.Grupo_Produto\", \n", "                       values=\"qtd\").fillna(0)\n", "\n", "# Escolher uma paleta de cores com base no número de colunas (grupos de produto)\n", "colors = sns.color_palette(\"tab20c\", n_colors=len(pivot.columns))\n", "\n", "# Criar figura e plotar um gráfico de barras empilhadas\n", "plt.figure(figsize=(14,7))\n", "pivot.plot(\n", "    kind=\"bar\", \n", "    stacked=True, \n", "    figsize=(14,7), \n", "    color=colors, \n", "    linewidth=0.5,\n", ")\n", "\n", "# Ajustar títulos e rótulos para melhor leitura\n", "plt.title(\"Distribuição de Compras por Faixa Etária e Grupo de Produto\", fontsize=14, weight=\"bold\")\n", "plt.xlabel(\"Faixa Etária\", fontsize=12)\n", "plt.ylabel(\"Quantidade\", fontsize=12)\n", "plt.xticks(rotation=0)\n", "\n", "# Posicionar a legenda fora do gráfico para evitar sobreposição\n", "plt.legend(title=\"Grupo de Produto\", bbox_to_anchor=(1.05, 1), loc=\"upper left\", ncol=3)\n", "\n", "plt.tight_layout()\n", "plt.show()  # Exibir o gráfico empilhado"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}